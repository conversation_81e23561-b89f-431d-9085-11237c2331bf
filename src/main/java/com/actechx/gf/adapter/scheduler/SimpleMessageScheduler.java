package com.actechx.gf.adapter.scheduler;

import com.actechx.gf.domain.service.InboundMessageService;
import com.actechx.gf.domain.service.OutboundMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Slf4j
@Component
@ConditionalOnProperty(
    name = "scheduler.simple.messaging.enabled",
    havingValue = "true",
    matchIfMissing = true)
public class SimpleMessageScheduler {

  private final OutboundMessageService outboundService;
  private final InboundMessageService inboundService;

  @Value("${scheduler.simple.messaging.batch-size:10}")
  private int batchSize;

  @Value("${scheduler.simple.messaging.max-retry-attempts:5}")
  private int maxRetryAttempts;

  @Scheduled(
      fixedDelayString = "${scheduler.simple.messaging.outbound.delay:5000}",
      initialDelay = 3000)
  public void processOutboundMessages() {
    try {
      outboundService.processMessages(batchSize, maxRetryAttempts);
    } catch (Exception e) {
      log.error("Error processing outbound messages", e);
    }
  }

  @Scheduled(
      fixedDelayString = "${scheduler.simple.messaging.inbound.delay:8000}",
      initialDelay = 5000)
  public void processInboundMessages() {
    try {
      inboundService.processMessages(batchSize, maxRetryAttempts);
    } catch (Exception e) {
      log.error("Error processing inbound messages", e);
    }
  }

  @Scheduled(
      fixedDelayString = "${scheduler.simple.messaging.notification.delay:10000}",
      initialDelay = 9000)
  public void processNotifications() {
    try {
      inboundService.processNotifications(batchSize);
    } catch (Exception e) {
      log.error("Error processing notifications", e);
    }
  }
}
