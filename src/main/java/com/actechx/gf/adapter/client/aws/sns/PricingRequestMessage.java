package com.actechx.gf.adapter.client.aws.sns;

import com.actechx.common.messaging.Message;
import com.actechx.gf.domain.enums.MessageGroup;
import com.actechx.gf.domain.enums.QuotationMessageStep;
import java.util.HashMap;
import java.util.Map;

public class PricingRequestMessage extends Message {

  public PricingRequestMessage(
      String rawMessageContent,
      String source,
      String type,
      Long originTenantId,
      String originMessageCode) {
    super(rawMessageContent, source, type);
    this.addHeaders(originTenantId, originMessageCode);
  }

  @Override
  protected void addHeaders(Long originTenantId, String originMessageCode) {
    Map<String, Object> headers = new HashMap<>();
    headers.put("MessageGroup", MessageGroup.QUOTATION.name());
    headers.put("MessageStep", QuotationMessageStep.PRICE_1.getStep());
    headers.put("OriginTenantId", originTenantId);
    headers.put("OriginMessageCode", originMessageCode);

    this.loadHeaders(headers);
  }
}
