package com.actechx.gf.adapter.client.aws.sqs;

import com.actechx.common.messaging.KafkaMessageHandler;
import com.actechx.common.messaging.MessageHandler;
import com.actechx.common.messaging.MessagePayload;
import com.actechx.common.utils.JsonUtils;
import com.actechx.gf.adapter.controller.form.SaleOrderDto;
import com.actechx.gf.domain.enums.InboundMessageType;
import com.actechx.gf.domain.enums.QuotationMessageStep;
import com.actechx.gf.domain.service.InboundMessageService;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class SaleOrderMessageHandler extends KafkaMessageHandler implements MessageHandler {
  private final InboundMessageService inboundMessageService;

  @Value("${kafka.topics.sale-order}")
  private String saleOrderSubscription;

  @KafkaListener(topics = "${kafka.topics.sale-order}")
  public void onMessageHandled(
      @Payload String rawMessage,
      @Header(value = "MessageGroup") String messageGroup,
      @Header(value = "MessageStep") String messageStep,
      @Header(value = "OriginTenantId") Long originTenantId,
      @Header(value = "OriginMessageCode") String originMessageCode,
      Acknowledgment acknowledgement) {
    log.info(
        "Received messageGroup: {}, messageStep: {}, raw: {}",
        messageGroup,
        messageStep,
        rawMessage);
    Map<String, Object> headers =
        Map.of(
            "MessageGroup", messageGroup,
            "MessageStep", messageStep,
            "OriginTenantId", originTenantId,
            "OriginMessageCode", originMessageCode);
    super.handleRawMessage(saleOrderSubscription, rawMessage, headers);
    acknowledgement.acknowledge();
  }

  @Override
  public void handleMessage(MessagePayload payload, Map<String, Object> headers) {
    var shouldHandle = false;
    InboundMessageType messageType = null;
    if (QuotationMessageStep.WAIT_TO_CONFIRM_3_1.getStep().equals(headers.get("MessageStep"))) {
      messageType = InboundMessageType.SALE_ORDER_CONFIRMATION;
      shouldHandle = true;
    } else if (QuotationMessageStep.WAIT_TO_CONFIRM_3_2
        .getStep()
        .equals(headers.get("MessageStep"))) {
      messageType = InboundMessageType.SALE_ORDER_CHANGE;
      shouldHandle = true;
    } else if (QuotationMessageStep.WAIT_TO_CONFIRM_3_3
        .getStep()
        .equals(headers.get("MessageStep"))) {
      messageType = InboundMessageType.SALE_ORDER_CANCELLATION;
      shouldHandle = true;
    }
    if (shouldHandle) {
      log.info("SaleOrderMessageHandler.handleMessage: {}", payload.getData());
      var form = JsonUtils.toObject(payload.getData().toString(), SaleOrderDto.class);
      inboundMessageService.createMessage(
          payload.getMessageId(),
          messageType,
          form.getPurchaserId(),
          JsonUtils.toJson(form),
          headers);
    } else {
      log.info(
          "SaleOrderMessageHandler.handleMessage: Unsupported message step: {}",
          headers.get("MessageStep"));
    }
  }
}
