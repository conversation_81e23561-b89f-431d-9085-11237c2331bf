package com.actechx.gf.adapter.client.aws.sqs;

import com.actechx.common.messaging.KafkaMessageHandler;
import com.actechx.common.messaging.MessageHandler;
import com.actechx.common.messaging.MessagePayload;
import com.actechx.common.utils.JsonUtils;
import com.actechx.gf.adapter.controller.form.CreateLocationRequest;
import com.actechx.gf.domain.enums.InboundMessageType;
import com.actechx.gf.domain.service.InboundMessageService;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class LocationMessageHandler extends KafkaMessageHandler implements MessageHandler {
  private final InboundMessageService inboundMessageService;

  @Value("${kafka.topics.location}")
  private String locationSubscription;

  @KafkaListener(topics = "${kafka.topics.location}")
  public void onMessageHandled(
      @Payload String rawMessage,
      @Header(value = "MessageGroup") String messageGroup,
      @Header(value = "MessageStep") String messageStep,
      @Header(value = "OriginTenantId") Long originTenantId,
      @Header(value = "OriginMessageCode") String originMessageCode,
      Acknowledgment acknowledgement) {
    log.info(
        "Received messageGroup: {}, messageStep: {}, raw: {}",
        messageGroup,
        messageStep,
        rawMessage);
    Map<String, Object> headers =
        Map.of(
            "MessageGroup", messageGroup,
            "MessageStep", messageStep,
            "OriginTenantId", originTenantId,
            "OriginMessageCode", originMessageCode);
    super.handleRawMessage(locationSubscription, rawMessage, headers);
    acknowledgement.acknowledge();
  }

  @Override
  public void handleMessage(MessagePayload payload, Map<String, Object> headers) {
    log.info("LocationMessageHandler.handleMessage: {}", payload.getData());
    var rawData = "";
    if (payload.getData() instanceof String) {
      rawData = payload.getData().toString();
    } else {
      rawData = JsonUtils.toJson(payload.getData());
    }
    var form = JsonUtils.toObject(rawData, CreateLocationRequest.class);
    inboundMessageService.createMessage(
        payload.getMessageId(),
        InboundMessageType.LOCATION,
        form.getTenantId(),
        JsonUtils.toJson(form),
        headers);
  }
}
