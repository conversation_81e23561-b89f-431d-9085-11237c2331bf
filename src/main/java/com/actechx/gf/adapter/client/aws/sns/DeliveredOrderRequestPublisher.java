package com.actechx.gf.adapter.client.aws.sns;

import com.actechx.common.messaging.KafkaMessagePublisher;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class DeliveredOrderRequestPublisher {

  private final KafkaMessagePublisher kafkaMessagePublisher;

  @Value("${spring.application.name}")
  private String applicationName;

  @Value("${kafka.topics.delivered-order}")
  private String deliveredOrderTopic;

  public void publishMessage(String rawData, Map<String, Object> headers) {
    var message =
        new DeliveredOrderRequestMessage(
            rawData,
            applicationName,
            "BASIC_MESSAGE",
            Long.parseLong(headers.get("OriginTenantId").toString()),
            headers.get("OriginMessageCode").toString());
    String messageKey = message.getMessageId();
    kafkaMessagePublisher.publishMessage(
        deliveredOrderTopic, messageKey, message, message.getHeaders());
  }
}
