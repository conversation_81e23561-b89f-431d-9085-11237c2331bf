package com.actechx.gf.adapter.client.rest;

import com.actechx.gf.adapter.controller.form.ShipmentOrderRequest;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

@HttpExchange("/protected/v1")
public interface GfShipmentClient {

  @PostExchange("/shipment-orders")
  void createShipmentOrder(@RequestBody ShipmentOrderRequest request);
}
