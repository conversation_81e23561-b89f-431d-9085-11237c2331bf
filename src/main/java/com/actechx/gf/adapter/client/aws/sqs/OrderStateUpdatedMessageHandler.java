package com.actechx.gf.adapter.client.aws.sqs;

import com.actechx.common.messaging.KafkaMessageHandler;
import com.actechx.common.messaging.MessageHandler;
import com.actechx.common.messaging.MessagePayload;
import com.actechx.common.utils.JsonUtils;
import com.actechx.gf.adapter.controller.form.OrderStateUpdatedDto;
import com.actechx.gf.domain.enums.InboundMessageType;
import com.actechx.gf.domain.enums.QuotationMessageStep;
import com.actechx.gf.domain.service.InboundMessageService;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class OrderStateUpdatedMessageHandler extends KafkaMessageHandler implements MessageHandler {
  private final InboundMessageService inboundMessageService;

  @Value("${kafka.topics.order-stage-update}")
  private String orderStageUpdatedSubscription;

  @KafkaListener(topics = "${kafka.topics.order-stage-update}")
  public void onMessageHandled(
      @Payload String rawMessage,
      @Header(value = "MessageGroup") String messageGroup,
      @Header(value = "MessageStep") String messageStep,
      @Header(value = "OriginTenantId") Long originTenantId,
      @Header(value = "OriginMessageCode") String originMessageCode,
      Acknowledgment acknowledgement) {
    log.info(
        "Received messageGroup: {}, messageStep: {}, raw: {}",
        messageGroup,
        messageStep,
        rawMessage);
    Map<String, Object> headers =
        Map.of(
            "MessageGroup", messageGroup,
            "MessageStep", messageStep,
            "OriginTenantId", originTenantId,
            "OriginMessageCode", originMessageCode);
    super.handleRawMessage(orderStageUpdatedSubscription, rawMessage, headers);
    acknowledgement.acknowledge();
  }

  @Override
  public void handleMessage(MessagePayload payload, Map<String, Object> headers) {
    var shouldHandle = false;
    InboundMessageType messageType = null;
    // TODO chọn trong bo trạng thái dưới đây để xử lý
    //    OPEN_1("OPEN.1"), // Đơn hàng giao kết thành công
    //    DELIVERING_1("DELIVERING.1"), // Vendor xuất kho
    //    DELIVERING_2("DELIVERING.2"), // HUB nhận hàng
    //    DELIVERING_3("DELIVERING.3"), // HUB chuyển ra nhà xe
    //    DELIVERING_4("DELIVERING.4"), // Hàng đến garage
    //    DELIVERED_1("DELIVERED.1"), // Garage confirm đã giao hàng
    //    PAID_1("PAID.1"), // Đã thanh toán
    //    CLOSE_1("CLOSE.1"), // Hoàn thành đơn bình thường
    //    CLOSE_2("CLOSE.2"), // Hoàn thành đơn có vấn đề
    if (QuotationMessageStep.OPEN_1.getStep().equals(headers.get("MessageStep"))) {
      messageType = InboundMessageType.ORDER_OPEN;
      shouldHandle = true;
    }
    if (shouldHandle) {
      log.info("OrderStateUpdatedMessageHandler.handleMessage: {}", payload.getData());
      var form = JsonUtils.toObject(payload.getData().toString(), OrderStateUpdatedDto.class);
      inboundMessageService.createMessage(
          payload.getMessageId(),
          messageType,
          form.getOriginTenantId(),
          JsonUtils.toJson(form),
          headers);
    } else {
      log.info(
          "OrderStateUpdatedMessageHandler.handleMessage: Unsupported message step: {}",
          headers.get("MessageStep"));
    }
  }
}
