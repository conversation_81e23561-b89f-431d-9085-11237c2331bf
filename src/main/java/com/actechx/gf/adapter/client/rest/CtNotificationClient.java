package com.actechx.gf.adapter.client.rest;

import com.actechx.gf.adapter.controller.form.NotificationRequestDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

@HttpExchange("/protected/v1")
public interface CtNotificationClient {

  @PostExchange("/notifications")
  void createNotification(@RequestBody NotificationRequestDto request);
}
