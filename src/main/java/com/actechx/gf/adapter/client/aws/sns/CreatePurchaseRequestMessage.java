package com.actechx.gf.adapter.client.aws.sns;

import com.actechx.common.messaging.Message;
import com.actechx.gf.domain.enums.MessageGroup;
import com.actechx.gf.domain.enums.QuotationMessageStep;
import java.util.HashMap;
import java.util.Map;

public class CreatePurchaseRequestMessage extends Message {

  public CreatePurchaseRequestMessage(
      String rawMessageContent,
      String source,
      String type,
      Long originTenantId,
      String originMessageCode) {
    super(rawMessageContent, source, type);
    this.addHeaders(originTenantId, originMessageCode);
  }

  @Override
  protected void addHeaders(Long originTenantId, String originMessageCode) {
    Map<String, Object> headers = new HashMap<>();
    headers.put("MessageGroup", MessageGroup.PO.name());
    headers.put("MessageStep", QuotationMessageStep.WAIT_TO_CONFIRM_1.getStep());
    headers.put("OriginTenantId", originTenantId);
    headers.put("OriginMessageCode", originMessageCode);

    this.loadHeaders(headers);
  }
}
