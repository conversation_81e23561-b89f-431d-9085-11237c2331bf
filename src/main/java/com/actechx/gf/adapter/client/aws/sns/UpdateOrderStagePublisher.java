package com.actechx.gf.adapter.client.aws.sns;

import com.actechx.common.messaging.KafkaMessagePublisher;
import com.actechx.gf.domain.enums.QuotationMessageStep;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class UpdateOrderStagePublisher {

  private final KafkaMessagePublisher kafkaMessagePublisher;

  @Value("${spring.application.name}")
  private String applicationName;

  @Value("${kafka.topics.order-stage-update}")
  private String orderStageUpdateTopic;

  public void publishMessage(String rawData, Map<String, Object> headers) {
    var message =
        new UpdateOrderStageMessage(
            rawData,
            applicationName,
            "BASIC_MESSAGE",
            Long.parseLong(headers.get("OriginTenantId").toString()),
            headers.get("OriginMessageCode").toString());
    message.setMessageStep(QuotationMessageStep.fromStep(headers.get("MessageStep").toString()));
    String messageKey = message.getMessageId();
    kafkaMessagePublisher.publishMessage(
        orderStageUpdateTopic, messageKey, message, message.getHeaders());
  }
}
