package com.actechx.gf.adapter.client.aws.sqs;

import com.actechx.common.messaging.KafkaMessageHandler;
import com.actechx.common.messaging.MessageHandler;
import com.actechx.common.messaging.MessagePayload;
import com.actechx.common.utils.JsonUtils;
import com.actechx.gf.adapter.controller.form.PricingProposalDto;
import com.actechx.gf.domain.enums.InboundMessageType;
import com.actechx.gf.domain.enums.QuotationMessageStep;
import com.actechx.gf.domain.service.InboundMessageService;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class PricingProposalMessageHandler extends KafkaMessageHandler implements MessageHandler {
  private final InboundMessageService inboundMessageService;

  @Value("${kafka.topics.pricing}")
  private String pricingProposalSubscription;

  @KafkaListener(topics = "${kafka.topics.pricing}")
  public void onMessageHandled(
      @Payload String rawMessage,
      @Header(value = "MessageGroup") String messageGroup,
      @Header(value = "MessageStep") String messageStep,
      @Header(value = "OriginTenantId") Long originTenantId,
      @Header(value = "OriginMessageCode") String originMessageCode,
      Acknowledgment acknowledgement) {
    log.info(
        "Received messageGroup: {}, messageStep: {}, raw: {}",
        messageGroup,
        messageStep,
        rawMessage);
    Map<String, Object> headers =
        Map.of(
            "MessageGroup", messageGroup,
            "MessageStep", messageStep,
            "OriginTenantId", originTenantId,
            "OriginMessageCode", originMessageCode);
    super.handleRawMessage(pricingProposalSubscription, rawMessage, headers);
    acknowledgement.acknowledge();
  }

  @Override
  public void handleMessage(MessagePayload payload, Map<String, Object> headers) {
    if (QuotationMessageStep.PRICE_2.getStep().equals(headers.get("MessageStep"))) {
      log.info("PricingProposalMessageHandler.handleMessage: {}", payload.getData());
      var rawData = "";
      if (payload.getData() instanceof String) {
        rawData = payload.getData().toString();
      } else {
        rawData = JsonUtils.toJson(payload.getData());
      }
      var form = JsonUtils.toObject(rawData, PricingProposalDto.class);
      inboundMessageService.createMessage(
          payload.getMessageId(),
          InboundMessageType.PRICING_PROPOSAL,
          form.getReplyTenantId(),
          JsonUtils.toJson(form),
          headers);
    } else {
      log.info(
          "PricingProposalMessageHandler: Unsupported message step: {}",
          headers.get("MessageStep"));
    }
  }
}
