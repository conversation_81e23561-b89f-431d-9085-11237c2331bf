package com.actechx.gf.adapter.client.rest;

import com.actechx.gf.adapter.controller.form.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import org.springframework.web.service.annotation.PutExchange;

@HttpExchange("/protected/v1")
public interface GfPurchaseClient {

  @PostExchange("/quotation-bids")
  void createQuotationBid(@RequestBody QuotationBidDto request);

  @PutExchange("/quotation-asks")
  void updateQuotationAsk(@RequestBody UpdateQuotationAskDto request);

  @PostExchange("/pricing-proposals")
  void createPricingProposal(@RequestBody PricingProposalDto request);

  @PutExchange("/purchase-orders")
  void updatePurchaseOrder(@RequestBody SaleOrderDto request);

  @PutExchange("/purchase-orders/status")
  void updatePurchaseOrderState(@RequestBody OrderStateUpdatedDto request);
}
