package com.actechx.gf.adapter.utils;

import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class DBUtils {

  private final EntityManager entityManager;

  @Value("${spring.jpa.properties.hibernate.default_schema}")
  private String defaultSchema;

  private final JdbcTemplate jdbcTemplate;

  public Long getNextSequence(String sequenceName) {
    try {
      String sql = String.format("SELECT \"%s\".get_next_number(?, ?)", defaultSchema);
      return jdbcTemplate.queryForObject(sql, Long.class, defaultSchema, sequenceName);
    } catch (Exception e) {
      throw new RuntimeException("Failed to generate sequence for: " + sequenceName, e);
    }
  }
}
