package com.actechx.gf.adapter.persistence.repository;

import com.actechx.gf.adapter.persistence.InboundMessageEntity;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface JpaInboundMessageRepository extends JpaRepository<InboundMessageEntity, Long> {

  Optional<InboundMessageEntity> findByMessageKey(String messageKey);

  @Query("SELECT m FROM InboundMessageEntity m WHERE m.status = 'PENDING' ORDER BY m.createdAt ASC")
  List<InboundMessageEntity> findPendingMessages(Pageable pageable);

  @Query(
      "SELECT m FROM InboundMessageEntity m WHERE m.status = 'RETRYING' AND m.attemptCount < :maxAttempts ORDER BY m.updatedAt ASC")
  List<InboundMessageEntity> findRetryableMessages(
      @Param("maxAttempts") int maxAttempts, Pageable pageable);

  @Query(
      "SELECT m FROM InboundMessageEntity m WHERE m.status = 'COMPLETED' AND m.isNotified = false ORDER BY m.processedAt ASC")
  List<InboundMessageEntity> findUnnotifiedMessages(Pageable pageable);

  @Modifying
  @Query(
      "DELETE FROM InboundMessageEntity m WHERE m.status = 'COMPLETED' AND m.processedAt < :cutoffDate")
  void deleteCompletedMessagesBefore(@Param("cutoffDate") LocalDateTime cutoffDate);
}
