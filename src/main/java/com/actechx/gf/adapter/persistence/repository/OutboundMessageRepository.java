package com.actechx.gf.adapter.persistence.repository;

import com.actechx.gf.adapter.persistence.OutboundMessageEntity;
import com.actechx.gf.adapter.persistence.mapper.OutboundMessageEntityMapper;
import com.actechx.gf.domain.model.OutboundMessage;
import jakarta.persistence.EntityManager;
import jakarta.persistence.LockModeType;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional
@RequiredArgsConstructor
public class OutboundMessageRepository
    implements com.actechx.gf.domain.repository.OutboundMessageRepository {

  private final JpaOutboundMessageRepository jpaRepository;
  private final OutboundMessageEntityMapper mapper;
  private final EntityManager entityManager;

  @Override
  public OutboundMessage save(OutboundMessage message) {
    OutboundMessageEntity entity = mapper.toJpaEntity(message);
    entity.setUpdatedAt(LocalDateTime.now());
    OutboundMessageEntity saved = jpaRepository.save(entity);
    return mapper.toDomain(saved);
  }

  @Override
  @Transactional(readOnly = true)
  public List<OutboundMessage> findPendingMessages(int limit) {
    return jpaRepository.findPendingMessages(PageRequest.of(0, limit)).stream()
        .map(mapper::toDomain)
        .toList();
  }

  @Override
  @Transactional(readOnly = true)
  public List<OutboundMessage> findRetryableMessages(int maxAttempts, int limit) {
    return jpaRepository.findRetryableMessages(maxAttempts, PageRequest.of(0, limit)).stream()
        .map(mapper::toDomain)
        .toList();
  }

  @Override
  public List<OutboundMessage> findPendingBatch(int batchSize) {
    String jpql =
        """
            SELECT qa FROM OutboundMessageEntity qa
            WHERE qa.status IN (com.actechx.gf.domain.enums.ProcessingStatus.PENDING,
                                          com.actechx.gf.domain.enums.ProcessingStatus.RETRYING)
            ORDER BY qa.createdAt ASC
            """;

    List<OutboundMessageEntity> entities =
        entityManager
            .createQuery(jpql, OutboundMessageEntity.class)
            .setMaxResults(batchSize)
            .getResultList();

    return entities.stream().map(mapper::toDomain).collect(Collectors.toList());
  }

  @Override
  public void lockForProcessing(List<Long> ids) {
    if (ids.isEmpty()) return;

    String jpql =
        """
            SELECT qa FROM OutboundMessageEntity qa
            WHERE qa.id IN :ids
            AND qa.status IN (com.actechx.gf.domain.enums.ProcessingStatus.PENDING,
                                        com.actechx.gf.domain.enums.ProcessingStatus.RETRYING)
            """;

    entityManager
        .createQuery(jpql, OutboundMessageEntity.class)
        .setParameter("ids", ids)
        .setLockMode(LockModeType.PESSIMISTIC_WRITE)
        .getResultList();
  }
}
