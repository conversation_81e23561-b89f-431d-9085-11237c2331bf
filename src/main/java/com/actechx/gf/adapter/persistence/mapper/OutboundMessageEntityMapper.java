package com.actechx.gf.adapter.persistence.mapper;

import com.actechx.gf.adapter.persistence.OutboundMessageEntity;
import com.actechx.gf.domain.model.OutboundMessage;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface OutboundMessageEntityMapper {

  OutboundMessageEntity toJpaEntity(OutboundMessage domain);

  OutboundMessage toDomain(OutboundMessageEntity jpaEntity);
}
