package com.actechx.gf.adapter.persistence.repository;

import com.actechx.gf.adapter.persistence.OutboundMessageEntity;
import java.util.List;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface JpaOutboundMessageRepository extends JpaRepository<OutboundMessageEntity, Long> {

  @Query(
      "SELECT m FROM OutboundMessageEntity m WHERE m.status = 'PENDING' ORDER BY m.createdAt ASC")
  List<OutboundMessageEntity> findPendingMessages(Pageable pageable);

  @Query(
      "SELECT m FROM OutboundMessageEntity m WHERE m.status = 'RETRYING' AND m.attemptCount < :maxAttempts ORDER BY m.updatedAt ASC")
  List<OutboundMessageEntity> findRetryableMessages(
      @Param("maxAttempts") int maxAttempts, Pageable pageable);
}
