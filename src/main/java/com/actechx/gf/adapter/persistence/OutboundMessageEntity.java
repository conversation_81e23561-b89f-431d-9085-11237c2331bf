package com.actechx.gf.adapter.persistence;

import com.actechx.gf.domain.enums.OutboundMessageType;
import com.actechx.gf.domain.enums.ProcessingStatus;
import com.vladmihalcea.hibernate.type.json.JsonType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import lombok.Data;
import org.hibernate.annotations.Type;

@Entity
@Table(name = "outbound_message")
@Data
public class OutboundMessageEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Enumerated(EnumType.STRING)
  @Column(name = "message_type", nullable = false, length = 50)
  private OutboundMessageType messageType;

  @Column(name = "tenant_id")
  private Long tenantId;

  @Column(name = "message_code", length = 255)
  private String messageCode;

  @Type(JsonType.class)
  @Column(name = "payload", columnDefinition = "json")
  private String payload;

  @Column(name = "message_group", length = 50)
  private String messageGroup;

  @Column(name = "message_step", length = 50)
  private String messageStep;

  @Column(name = "origin_tenant_id")
  private Long originTenantId;

  @Enumerated(EnumType.STRING)
  @Column(name = "status", nullable = false, length = 20)
  private ProcessingStatus status;

  @Column(name = "attempt_count", nullable = false)
  private Integer attemptCount = 0;

  @Column(name = "last_error", columnDefinition = "TEXT")
  private String lastError;

  @Column(name = "created_at", nullable = false)
  private LocalDateTime createdAt;

  @Column(name = "updated_at", nullable = false)
  private LocalDateTime updatedAt;

  @Column(name = "processed_at")
  private LocalDateTime processedAt;
}
