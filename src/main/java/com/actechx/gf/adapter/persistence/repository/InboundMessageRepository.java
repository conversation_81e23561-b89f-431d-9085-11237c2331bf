package com.actechx.gf.adapter.persistence.repository;

import com.actechx.gf.adapter.persistence.InboundMessageEntity;
import com.actechx.gf.adapter.persistence.mapper.InboundMessageEntityMapper;
import com.actechx.gf.domain.model.InboundMessage;
import jakarta.persistence.EntityManager;
import jakarta.persistence.LockModeType;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional
@RequiredArgsConstructor
public class InboundMessageRepository
    implements com.actechx.gf.domain.repository.InboundMessageRepository {

  private final JpaInboundMessageRepository jpaRepository;
  private final InboundMessageEntityMapper mapper;
  private final EntityManager entityManager;

  @Override
  public InboundMessage save(InboundMessage message) {
    InboundMessageEntity entity = mapper.toJpaEntity(message);
    entity.setUpdatedAt(LocalDateTime.now());
    InboundMessageEntity saved = jpaRepository.save(entity);
    return mapper.toDomain(saved);
  }

  @Override
  @Transactional(readOnly = true)
  public Optional<InboundMessage> findByMessageKey(String messageKey) {
    return jpaRepository.findByMessageKey(messageKey).map(mapper::toDomain);
  }

  @Override
  @Transactional(readOnly = true)
  public List<InboundMessage> findPendingMessages(int limit) {
    return jpaRepository.findPendingMessages(PageRequest.of(0, limit)).stream()
        .map(mapper::toDomain)
        .toList();
  }

  @Override
  @Transactional(readOnly = true)
  public List<InboundMessage> findRetryableMessages(int maxAttempts, int limit) {
    return jpaRepository.findRetryableMessages(maxAttempts, PageRequest.of(0, limit)).stream()
        .map(mapper::toDomain)
        .toList();
  }

  @Override
  @Transactional(readOnly = true)
  public List<InboundMessage> findUnnotifiedMessages(int limit) {
    return jpaRepository.findUnnotifiedMessages(PageRequest.of(0, limit)).stream()
        .map(mapper::toDomain)
        .toList();
  }

  @Override
  public List<InboundMessage> findPendingBatch(int batchSize) {
    String jpql =
        """
            SELECT qa FROM InboundMessageEntity qa
            WHERE qa.status IN (com.actechx.gf.domain.enums.ProcessingStatus.PENDING,
                                          com.actechx.gf.domain.enums.ProcessingStatus.RETRYING)
            ORDER BY qa.createdAt ASC
            """;

    List<InboundMessageEntity> entities =
        entityManager
            .createQuery(jpql, InboundMessageEntity.class)
            .setMaxResults(batchSize)
            .getResultList();

    return entities.stream().map(mapper::toDomain).collect(Collectors.toList());
  }

  @Override
  public void lockForProcessing(List<Long> ids) {
    if (ids.isEmpty()) return;

    String jpql =
        """
            SELECT qa FROM InboundMessageEntity qa
            WHERE qa.id IN :ids
            AND qa.status IN (com.actechx.gf.domain.enums.ProcessingStatus.PENDING,
                                        com.actechx.gf.domain.enums.ProcessingStatus.RETRYING)
            """;

    entityManager
        .createQuery(jpql, InboundMessageEntity.class)
        .setParameter("ids", ids)
        .setLockMode(LockModeType.PESSIMISTIC_WRITE)
        .getResultList();
  }
}
