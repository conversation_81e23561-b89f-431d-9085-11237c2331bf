package com.actechx.gf.adapter.config;

import com.actechx.gf.adapter.client.rest.CtNotificationClient;
import com.actechx.gf.adapter.client.rest.GfInventoryClient;
import com.actechx.gf.adapter.client.rest.GfPurchaseClient;
import com.actechx.gf.adapter.client.rest.GfShipmentClient;
import com.actechx.gf.adapter.controller.error.GenericResponseErrorHandlerFactory;
import java.time.Duration;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestClient;
import org.springframework.web.client.support.RestClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

@RequiredArgsConstructor
@Configuration
public class HttpClientConfig {

  @Value("${gf-purchase-service.url}")
  private String gfPurchaseClientBaseUrl;

  @Value("${gf-shipment-service.url}")
  private String gfShipmentClientBaseUrl;

  @Value("${gf-inventory-service.url}")
  private String gfInventoryClientBaseUrl;

  @Value("${ct-notification-service.url}")
  private String ctNotificationClientBaseUrl;

  @Value("${internal-service.api-key}")
  private String internalApiKey;

  private final GenericResponseErrorHandlerFactory handlerFactory;

  @Bean
  public GfPurchaseClient gfPurchaseClient() {
    return createClient(GfPurchaseClient.class, "gf-purchase-client", gfPurchaseClientBaseUrl);
  }

  @Bean
  public GfShipmentClient gfShipmentClient() {
    return createClient(GfShipmentClient.class, "gf-shipment-client", gfShipmentClientBaseUrl);
  }

  @Bean
  public GfInventoryClient gfInventoryClient() {
    return createClient(GfInventoryClient.class, "gf-inventory-client", gfInventoryClientBaseUrl);
  }

  @Bean
  public CtNotificationClient ctNotificationClient() {
    return createClient(CtNotificationClient.class, "ct-notification-client", ctNotificationClientBaseUrl);
  }

  private <T> T createClient(Class<T> clientClass, String clientName, String baseUrl) {
    var clientFactory = new SimpleClientHttpRequestFactory();
    clientFactory.setConnectTimeout(Duration.ofSeconds(3));
    clientFactory.setReadTimeout(Duration.ofSeconds(5));
    var builder =
        RestClient.builder()
            .baseUrl(baseUrl)
            .requestFactory(clientFactory)
            .defaultStatusHandler(
                HttpStatusCode::isError,
                (request, response) -> {
                  handlerFactory
                      .create(clientName)
                      .handleError(request.getURI(), request.getMethod(), response);
                })
            .defaultHeader("Content-Type", "application/json")
            .defaultHeader("Accept", "application/json")
            .defaultHeader("x-api-key", internalApiKey);

    HttpServiceProxyFactory factory =
        HttpServiceProxyFactory.builderFor(RestClientAdapter.create(builder.build())).build();

    return factory.createClient(clientClass);
  }
}
