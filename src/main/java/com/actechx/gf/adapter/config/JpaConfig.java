package com.actechx.gf.adapter.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Configuration
@EnableJpaRepositories(basePackages = {"com.actechx.gf.adapter"})
@EnableTransactionManagement
@EnableScheduling
@EnableJpaAuditing
public class JpaConfig {}
