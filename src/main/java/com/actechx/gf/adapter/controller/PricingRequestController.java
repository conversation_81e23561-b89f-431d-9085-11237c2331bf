package com.actechx.gf.adapter.controller;

import com.actechx.common.dto.ApiResponse;
import com.actechx.common.utils.JsonUtils;
import com.actechx.common.utils.ResponseUtil;
import com.actechx.gf.adapter.controller.form.PricingRequestDto;
import com.actechx.gf.app.service.SimpleMessagingApplicationService;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/protected/v1/pricing")
@RequiredArgsConstructor
public class PricingRequestController {

  private final SimpleMessagingApplicationService simpleMessagingApplicationService;

  @PostMapping
  public ResponseEntity<ApiResponse<String>> createPricingRequests(
      @RequestHeader(name = "x-api-key") String apiKey,
      @Valid @RequestBody List<PricingRequestDto> pricingRequestDtos) {

    var element = pricingRequestDtos.stream().findFirst().orElseThrow();
    simpleMessagingApplicationService.createPricingRequestMessage(
        element.getQuotationAskCode(),
        element.getOriginTenantId(),
        JsonUtils.toJson(pricingRequestDtos));

    return ResponseEntity.status(HttpStatus.CREATED).body(ResponseUtil.success("OK"));
  }
}
