package com.actechx.gf.adapter.controller;

import com.actechx.common.dto.ApiResponse;
import com.actechx.common.utils.JsonUtils;
import com.actechx.common.utils.ResponseUtil;
import com.actechx.gf.adapter.controller.form.CancelPurchaseRequestDto;
import com.actechx.gf.adapter.controller.form.CreatePurchaseRequestDto;
import com.actechx.gf.adapter.controller.form.PurchaseOrderStageRequest;
import com.actechx.gf.adapter.controller.form.SaleOrderDto;
import com.actechx.gf.app.service.SimpleMessagingApplicationService;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/protected/v1/purchases")
@RequiredArgsConstructor
public class PurchaseRequestController {

  private final SimpleMessagingApplicationService simpleMessagingApplicationService;

  @PostMapping
  public ResponseEntity<ApiResponse<String>> createPurchaseRequests(
      @RequestHeader(name = "x-api-key") String apiKey,
      @Valid @RequestBody CreatePurchaseRequestDto purchaseRequestDto) {

    simpleMessagingApplicationService.createPurchaseRequestMessage(
        purchaseRequestDto.getCode(),
        purchaseRequestDto.getPurchaserId(),
        JsonUtils.toJson(purchaseRequestDto));

    return ResponseEntity.status(HttpStatus.CREATED).body(ResponseUtil.success("OK"));
  }

  @PostMapping("/confirm")
  public ResponseEntity<ApiResponse<String>> confirmPurchaseRequests(
      @RequestHeader(name = "x-api-key") String apiKey,
      @Valid @RequestBody List<SaleOrderDto> requestDto) {
    var element = requestDto.stream().findFirst().orElseThrow();
    simpleMessagingApplicationService.confirmPurchaseRequestMessage(
        element.getPurchaseRequestCode(), element.getPurchaserId(), JsonUtils.toJson(requestDto));
    return ResponseEntity.status(HttpStatus.CREATED).body(ResponseUtil.success("OK"));
  }

  @PutMapping("/cancel")
  public ResponseEntity<ApiResponse<String>> cancelPurchaseRequests(
      @RequestHeader(name = "x-api-key") String apiKey,
      @Valid @RequestBody CancelPurchaseRequestDto requestDto) {

    simpleMessagingApplicationService.cancelPurchaseRequestMessage(
        requestDto.getCode(), requestDto.getPurchaserId(), JsonUtils.toJson(requestDto));

    return ResponseEntity.status(HttpStatus.CREATED).body(ResponseUtil.success("OK"));
  }

  @PostMapping("/confirm-received")
  public ResponseEntity<ApiResponse<String>> confirmReceivedPurchaseRequests(
      @RequestHeader(name = "x-api-key") String apiKey,
      @Valid @RequestBody PurchaseOrderStageRequest requestDto) {
    simpleMessagingApplicationService.confirmDeliveredShipmentOrderMessage(
        requestDto.getCodes().getFirst(), null, JsonUtils.toJson(requestDto));
    return ResponseEntity.status(HttpStatus.CREATED).body(ResponseUtil.success("OK"));
  }
}
