package com.actechx.gf.adapter.controller.form;

import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PricingRequestDto {

  private String quotationAskCode;
  private Long originTenantId;
  private List<AskedSparePart> askedSpareParts;
  private String targetTenantId;
  private LocalDateTime createdAt;
  private String createdBy;

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class AskedSparePart {
    private String code;
    private String partNameInput;
    private String partNameUnit;
    private String segment;
    private Integer quantity;
    private String refCode;
    private String tenantId;
  }
}
