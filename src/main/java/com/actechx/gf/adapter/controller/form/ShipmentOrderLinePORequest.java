package com.actechx.gf.adapter.controller.form;

import java.util.List;
import lombok.Data;

@Data
public class ShipmentOrderLinePORequest {
  private Long id;

  private Long shipmentOrderLineId;

  private Long purchaseOrderId;

  private String purchaseOrderCode;

  private Long snapshotTenantId;

  private String snapshotTenantName;

  private String snapshotTenantAddress;

  private String snapshotTenantPhoneNumber;

  private Long carrierId;

  private String carrierName;

  private String carrierRoute;

  private String note;

  private Boolean isLast;

  private Boolean isDeleted;

  private String status;

  private List<ShipmentAttachmentRequest> attachments;
}
