package com.actechx.gf.adapter.controller.form;

import com.actechx.common.enums.TenantType;
import com.actechx.gf.domain.enums.NotificationChannel;
import com.actechx.gf.domain.enums.NotificationType;
import com.actechx.gf.domain.enums.TargetClient;
import lombok.Data;

import java.util.Map;
import java.util.Set;

@Data
public class NotificationRequestDto {
  private Recipient recipient;
  private NotificationType notificationType;
  private Map<String, Object> placeholders;
  private String campaign;
  private NotificationChannel channel;

  @Data
  public static class Recipient {
    private TargetUser specificationsUser;

    private Long tenantId; // to tenant id
    private TenantType tenantType; // to tenant type

    private TargetClient clientType;
  }

  @Data
  public static class TargetUser {
    private Set<String> userIds;
    private TargetClient userType;
  }
}

