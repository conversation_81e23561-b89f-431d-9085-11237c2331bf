package com.actechx.gf.adapter.controller.form;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreatePurchaseRequestDto {
  private String code; // PurchaseRequestCode
  private Long purchaserId; // tenant garage Id
  private String status;
  private String purchaseRequestDataList; // List PurchaseRequestData
  private String purchaseOrderList; // List PurchaseOrder
  private String purchaserInformation; // Thông tin garage PurchaserInformation
  private String purchaserCode; // tenant garage code
  private String purchaseSource;
  private String saleSource; // null
}
