package com.actechx.gf.adapter.controller;

import com.actechx.common.dto.ApiResponse;
import com.actechx.common.utils.JsonUtils;
import com.actechx.common.utils.ResponseUtil;
import com.actechx.gf.adapter.controller.form.UpdateOrderStateDto;
import com.actechx.gf.domain.enums.MessageGroup;
import com.actechx.gf.domain.enums.OutboundMessageType;
import com.actechx.gf.domain.service.OutboundMessageService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/protected/v1/orders")
@RequiredArgsConstructor
public class OrderController {

  private final OutboundMessageService outboundMessageService;

  @PutMapping("/state")
  public ResponseEntity<ApiResponse<String>> updateOrderState(
      @RequestHeader(name = "x-api-key") String apiKey,
      @Valid @RequestBody UpdateOrderStateDto requestDto) {
    // TODO base on reqeuestDto để tao outbound message với QuotationMessageStep tương ứng
    outboundMessageService.createMessage(
        OutboundMessageType.CANCEL_PURCHASE_REQUEST,
        requestDto.getTenantId(),
        requestDto.getCode(),
        JsonUtils.toJson(requestDto),
        MessageGroup.PO.name(),
        null); // TODO

    return ResponseEntity.status(HttpStatus.CREATED).body(ResponseUtil.success("OK"));
  }
}
