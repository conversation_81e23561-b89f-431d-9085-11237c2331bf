package com.actechx.gf.adapter.controller.form;

import com.actechx.gf.domain.enums.AttachmentOwner;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Data
public class CreateQuotationAskDto {
  @NotNull private Long id;
  @NotBlank private String tenantId;

  @NotBlank private String tenantName;

  @NotBlank private String code;

  private String tenantPhoneNumber;
  private String tenantOpsArea;
  private String tenantAddress;
  private String tenantOpsRegion;
  private String askNote;
  private Boolean isInvoiceRequired;

  @Valid private AskedVehicleDto askedVehicle;

  @Valid private List<AttachmentDto> attachments;

  @Valid private List<SparePartDto> spareParts;

  private LocalDateTime createdAt;
  private String createdBy;

  @Data
  public static class AskedVehicleDto {
    private Long id;
    private String carBrand;
    private String carModel;
    private String yearOfManufacture;
    private String carType;
    private String trimsLevel;
    private String vin;
  }

  @Data
  public static class AttachmentDto {
    private Long id;
    private AttachmentOwner owner;
    @NotBlank private String attachmentUrl;
    private String note;
  }

  @Data
  public static class SparePartDto {
    private Long id;
    private String code;
    @NotBlank private String partNameInput;
    private String partNameUnit;
    private String refCode;
    private Long tenantId;
  }
}
