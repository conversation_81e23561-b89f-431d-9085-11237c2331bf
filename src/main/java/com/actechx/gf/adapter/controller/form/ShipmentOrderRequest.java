package com.actechx.gf.adapter.controller.form;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ShipmentOrderRequest {
  @NotNull private Long id;

  @NotBlank private String code;

  private String type;

  private Long locationId;

  private String note;

  private String status;

  private List<ShipmentOrderLineRequest> shipmentOrderLines;

  private List<ShipmentOrderLinePORequest> shipmentOrderLinePOs;

  private List<ShipmentOrderLineSORequest> shipmentOrderLineSOs;

  private List<ShipmentAttachmentRequest> attachments;

  private Boolean isDeleted;
}
