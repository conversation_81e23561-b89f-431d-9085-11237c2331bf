package com.actechx.gf.adapter.controller;

import com.actechx.common.dto.ApiResponse;
import com.actechx.common.utils.JsonUtils;
import com.actechx.common.utils.ResponseUtil;
import com.actechx.gf.adapter.controller.form.CreateQuotationAskDto;
import com.actechx.gf.app.service.SimpleMessagingApplicationService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/protected/v1/quotation-asks")
@RequiredArgsConstructor
public class QuotationAskController {

  private final SimpleMessagingApplicationService simpleMessagingApplicationService;

  @PostMapping
  public ResponseEntity<ApiResponse<String>> createQuotationAsk(
      @RequestHeader(name = "x-api-key") String apiKey,
      @Valid @RequestBody CreateQuotationAskDto request) {

    var tenantId = Long.parseLong(request.getTenantId());
    simpleMessagingApplicationService.createQuotationAskMessage(
        request.getCode(), tenantId, JsonUtils.toJson(request));

    return ResponseEntity.status(HttpStatus.CREATED).body(ResponseUtil.success("OK"));
  }
}
