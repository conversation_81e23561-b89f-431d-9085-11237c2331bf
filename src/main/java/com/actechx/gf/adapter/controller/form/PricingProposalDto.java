package com.actechx.gf.adapter.controller.form;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PricingProposalDto {

  private String quotationAskCode;
  private Long replyTenantId;
  private String pricingRequestInformation;
  private List<SparePartPriceLineItems> sparePartPriceLineItems;
  private LocalDateTime createdAt;
  private String createdBy;

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class SparePartPriceLineItems {
    private String sparePartInputCode;
    private String segment;
    private BigDecimal materialPrice;
    private BigDecimal servicingPrice;
    private String currency;
    private String note;
    private Integer quantity;
    private String refCode;
    private String unit;
  }
}
