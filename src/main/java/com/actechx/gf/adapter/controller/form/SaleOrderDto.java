package com.actechx.gf.adapter.controller.form;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaleOrderDto {
  private Long id;
  private Long purchaseRequestId;
  private String purchaseRequestCode;
  private String code; // purchaseOrderCode
  private String source;
  private Long poId;
  private Long transportOrderId;
  private Long supplierId;
  private Long purchaserId;
  private String purchaserName;
  private String paymentMethod;
  private Boolean isBestPrice;
  private String quotationAskCode;
  private String stage;
  private String status;
  private String note;
  private BigDecimal transportCost;
  private List<POProductRequestDTO> soProducts;
  private LocalDateTime createdAt;
  private String createdBy;

  @AllArgsConstructor
  @NoArgsConstructor
  @Data
  public static class POProductRequestDTO {
    private Long id;
    private Long productId;
    private Long quantity;
    private BigDecimal unitPrice;
    private String unit;
    private String segment;
    private Long supplierId;
    private String requestedProductName;
    private Long saleOrderId;
    private String quotationAskCode;
  }
}
