package com.actechx.gf.adapter.controller.form;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CancelPurchaseRequestDto {
  @NotBlank private String code;
  @NotNull private Long purchaserId; // tenantId
  @NotBlank private String status;
  @NotBlank private String note;
}
