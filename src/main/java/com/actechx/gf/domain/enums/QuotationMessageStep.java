package com.actechx.gf.domain.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

public enum QuotationMessageStep {
  ASK_1("ASK.1"),
  ASK_2("ASK.2"),
  BID_1("BID.1"),
  PRICE_1("PRICE.1"),
  PRICE_2("PRICE.2"),
  PRICE_3("PRICE.3"),

  WAIT_TO_CONFIRM_1("WAIT_TO_CONFIRM.1"),
  CANCEL_1("CANCEL.1"),
  WAIT_TO_CONFIRM_3_1("WAIT_TO_CONFIRM.3.1"),
  WAIT_TO_CONFIRM_3_2("WAIT_TO_CONFIRM.3.2"),
  WAIT_TO_CONFIRM_3_3("WAIT_TO_CONFIRM.3.3"),

  OPEN_1("OPEN.1"), // Đơn hàng giao kết thành công
  DELIVERING_1("DELIVERING.1"), // Vendor xuất kho
  DELIVERING_2("DELIVERING.2"), // HUB nhận hàng
  DELIVERING_3("DELIVERING.3"), // HUB chuyển ra nhà xe
  DELIVERING_4("DELIVERING.4"), // Hàng đến garage
  DELIVERED_1("DELIVERED.1"), // Garage confirm đã giao hàng
  PAID_1("PAID.1"), // Đã thanh toán
  CLOSE_1("CLOSE.1"), // Hoàn thành đơn bình thường
  CLOSE_2("CLOSE.2"), // Hoàn thành đơn có vấn đề
  ;

  private static final Map<String, QuotationMessageStep> STEP_MAP;

  static {
    STEP_MAP =
        Arrays.stream(values()).collect(Collectors.toMap(QuotationMessageStep::getStep, e -> e));
  }

  public static QuotationMessageStep fromStep(String step) {
    QuotationMessageStep result = STEP_MAP.get(step);
    if (result == null) {
      throw new IllegalArgumentException("No QuotationMessageStep found for step: " + step);
    }
    return result;
  }

  private String step;

  QuotationMessageStep(String step) {
    this.step = step;
  }

  public String getStep() {
    return step;
  }

  public void setStep(String step) {
    this.step = step;
  }
}
