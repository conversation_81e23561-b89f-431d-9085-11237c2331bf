package com.actechx.gf.domain.service;

import com.actechx.common.utils.JsonUtils;
import com.actechx.gf.adapter.client.rest.GfInventoryClient;
import com.actechx.gf.adapter.client.rest.GfPurchaseClient;
import com.actechx.gf.adapter.client.rest.GfShipmentClient;
import com.actechx.gf.adapter.controller.form.*;
import com.actechx.gf.domain.enums.InboundMessageType;
import com.actechx.gf.domain.model.InboundMessage;
import com.actechx.gf.domain.repository.InboundMessageRepository;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class InboundMessageService {

  private final InboundMessageRepository repository;
  private final GfPurchaseClient gfPurchaseClient;
  private final GfShipmentClient gfShipmentClient;
  private final GfInventoryClient gfInventoryClient;

  public InboundMessage createMessage(
      String messageKey,
      InboundMessageType messageType,
      Long tenantId,
      String payload,
      Map<String, Object> headers) {

    var messageCode = headers.get("OriginMessageCode").toString();
    var messageGroup = headers.get("MessageGroup").toString();
    var messageStep = headers.get("MessageStep").toString();
    var originTenantId = Long.parseLong(headers.get("OriginTenantId").toString());
    InboundMessage message =
        new InboundMessage(
            messageKey,
            messageType,
            tenantId,
            messageCode,
            payload,
            messageGroup,
            messageStep,
            originTenantId);

    try {
      return repository.save(message);
    } catch (DataIntegrityViolationException e) {
      log.warn("Duplicate message key: {}", messageKey);
      return repository.findByMessageKey(messageKey).orElse(null);
    }
  }

  public void processMessages(int batchSize, int maxRetryAttempts) {
    List<InboundMessage> pendingMessages = repository.findPendingBatch(batchSize);
    if (pendingMessages.isEmpty()) {
      log.info("No pending inbound messages found");
      return;
    }

    var processingIds = pendingMessages.stream().map(InboundMessage::getId).toList();

    repository.lockForProcessing(processingIds);

    for (InboundMessage message : pendingMessages) {
      processMessage(message, maxRetryAttempts);
    }

    log.debug("Processed number of inbound messages: {}", pendingMessages.size());
  }

  private void processMessage(InboundMessage message, int maxRetryAttempts) {
    try {
      switch (message.getMessageType()) {
        case InboundMessageType.QUOTATION_BID -> {
          log.info(
              "Calling gf-purchase to create quotation bid with payload: {}", message.getPayload());
          gfPurchaseClient.createQuotationBid(
              JsonUtils.toObject(message.getPayload(), QuotationBidDto.class));
        }
        case InboundMessageType.QUOTATION_ASK_UPDATE -> {
          log.info(
              "Calling gf-purchase to update quotation ask with payload: {}", message.getPayload());
          gfPurchaseClient.updateQuotationAsk(
              JsonUtils.toObject(message.getPayload(), UpdateQuotationAskDto.class));
        }
        case InboundMessageType.PRICING_PROPOSAL -> {
          log.info(
              "Calling gf-purchase to create pricing proposal with payload: {}",
              message.getPayload());
          gfPurchaseClient.createPricingProposal(
              JsonUtils.toObject(message.getPayload(), PricingProposalDto.class));
        }
        case InboundMessageType.SALE_ORDER_CONFIRMATION,
            InboundMessageType.SALE_ORDER_CHANGE,
            InboundMessageType.SALE_ORDER_CANCELLATION -> {
          log.info(
              "Calling gf-purchase to update sale-order with payload: {}", message.getPayload());
          gfPurchaseClient.updatePurchaseOrder(
              JsonUtils.toObject(message.getPayload(), SaleOrderDto.class));
        }
        // TODO bộ trạng thái update state or order
        case InboundMessageType.ORDER_OPEN -> {
          log.info("Calling gf-purchase to open order with payload: {}", message.getPayload());
          gfPurchaseClient.updatePurchaseOrderState(
              JsonUtils.toObject(message.getPayload(), OrderStateUpdatedDto.class));
        }
        case InboundMessageType.LOCATION -> {
          log.info(
              "Calling gf-inventory to create location with payload: {}", message.getPayload());
          gfInventoryClient.createLocation(
              JsonUtils.toObject(message.getPayload(), CreateLocationRequest.class));
        }
        case InboundMessageType.SHIPMENT_ORDER -> {
          log.info(
              "Calling gf-shipment to create shipment-order with payload: {}",
              message.getPayload());
          gfShipmentClient.createShipmentOrder(
              JsonUtils.toObject(message.getPayload(), ShipmentOrderRequest.class));
        }
        case InboundMessageType.SHIPMENT_ORDER_STAGE -> {
          log.info(
              "Calling gf-shipment to update shipment-order with payload: {}",
              message.getPayload());
          gfShipmentClient.createShipmentOrder(
              JsonUtils.toObject(message.getPayload(), ShipmentOrderRequest.class));
        }
        default ->
            throw new IllegalArgumentException("Unknown message type: " + message.getMessageType());
      }

      message.markCompleted();
      repository.save(message);

      log.debug("Processed inbound message {} via API", message.getMessageKey());

    } catch (Exception e) {
      message.incrementAttempt();

      if (message.getAttemptCount() >= maxRetryAttempts) {
        message.markFailed(e.getMessage());
        log.error("Message {} failed permanently: {}", message.getMessageKey(), e.getMessage());
      } else {
        log.warn(
            "Message {} failed, attempt {}: {}",
            message.getMessageKey(),
            message.getAttemptCount(),
            e.getMessage());
      }

      repository.save(message);
    }
  }

  public void processNotifications(int batchSize) {
    List<InboundMessage> unnotifiedMessages = repository.findUnnotifiedMessages(batchSize);

    for (InboundMessage message : unnotifiedMessages) {
      try {
        // TODO: Implement notification logic (SNS to notification queue)
        message.markNotified();
        repository.save(message);
        log.debug("Sent notification for message {}", message.getMessageKey());
      } catch (Exception e) {
        log.error(
            "Failed to send notification for message {}: {}",
            message.getMessageKey(),
            e.getMessage());
      }
    }

    log.debug("Processed {} notifications", unnotifiedMessages.size());
  }
}
