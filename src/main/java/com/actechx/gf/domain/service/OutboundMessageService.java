package com.actechx.gf.domain.service;

import com.actechx.gf.adapter.client.aws.sns.*;
import com.actechx.gf.domain.enums.OutboundMessageType;
import com.actechx.gf.domain.model.OutboundMessage;
import com.actechx.gf.domain.repository.OutboundMessageRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class OutboundMessageService {

  private final NotificationMessageService notificationMessageService;
  private final OutboundMessageRepository repository;
  private final QuotationAskPublisher quotationAskPublisher;
  private final PricingRequestPublisher pricingRequestPublisher;
  private final CreatePurchaseRequestPublisher createPurchaseRequestPublisher;
  private final CancelPurchaseRequestPublisher cancelPurchaseRequestPublisher;
  private final UpdateOrderStagePublisher updateOrderStatePublisher;
  private final DeliveredOrderRequestPublisher deliveredOrderRequestPublisher;

  public OutboundMessage createMessage(
      OutboundMessageType messageType,
      Long tenantId,
      String messageCode,
      String payload,
      String messageGroup,
      String messageStep) {

    OutboundMessage message =
        new OutboundMessage(
            messageType,
            tenantId,
            messageCode,
            payload,
            messageGroup,
            messageStep,
            tenantId.toString());
    return repository.save(message);
  }

  public void processMessages(int batchSize, int maxRetryAttempts) {
    List<OutboundMessage> pendingMessages = repository.findPendingBatch(batchSize);

    if (pendingMessages.isEmpty()) {
      log.info("No pending outbound messages found");
      return;
    }

    var processingIds = pendingMessages.stream().map(OutboundMessage::getId).toList();

    repository.lockForProcessing(processingIds);

    for (OutboundMessage message : pendingMessages) {
      processMessage(message, maxRetryAttempts);
    }

    log.debug("Processed number of outbound messages: {}", pendingMessages.size());
  }

  private void processMessage(OutboundMessage message, int maxRetryAttempts) {
    try {
      // Build headers map as expected by the concrete publishers
      Map<String, Object> headers = new HashMap<>();
      headers.put("MessageGroup", message.getMessageGroup());
      headers.put("MessageStep", message.getMessageStep());
      headers.put("OriginTenantId", message.getOriginTenantId());
      headers.put("OriginMessageCode", message.getMessageCode());

      switch (message.getMessageType()) {
        case OutboundMessageType.QUOTATION_ASK ->
            quotationAskPublisher.publishMessage(message.getPayload(), headers);
        case OutboundMessageType.PRICING_REQUEST -> {
          pricingRequestPublisher.publishMessage(message.getPayload(), headers);
//          notificationMessageService.processPricingRequest(message.getPayload());
        }
        case OutboundMessageType.CREATE_PURCHASE_REQUEST ->
            createPurchaseRequestPublisher.publishMessage(message.getPayload(), headers);
        case OutboundMessageType.CONFIRM_PURCHASE_REQUEST -> {
          updateOrderStatePublisher.publishMessage(message.getPayload(), headers);
//          notificationMessageService.processConfirmPurchaseRequest(message.getPayload());
        }
        case OutboundMessageType.CANCEL_PURCHASE_REQUEST -> {
          cancelPurchaseRequestPublisher.publishMessage(message.getPayload(), headers);
//          notificationMessageService.processCancelPurchaseOrders(message.getPayload());
        }
        case OutboundMessageType.DELIVERED_SHIPMENT_ORDER -> {
          deliveredOrderRequestPublisher.publishMessage(message.getPayload(), headers);
//          notificationMessageService.processDeliveredShipmentOrder(message.getPayload());
        }

        default ->
            throw new IllegalArgumentException(
                "Unsupported message type: " + message.getMessageType());
      }

      message.markCompleted();
      repository.save(message);

      log.debug("Published message {} to SNS", message.getId());

    } catch (Exception e) {
      message.incrementAttempt();

      if (message.getAttemptCount() >= maxRetryAttempts) {
        message.markFailed(e.getMessage());
        log.error("Message {} failed permanently: {}", message.getId(), e.getMessage());
      } else {
        log.warn(
            "Message {} failed, attempt {}: {}",
            message.getId(),
            message.getAttemptCount(),
            e.getMessage());
      }

      repository.save(message);
    }
  }
}
