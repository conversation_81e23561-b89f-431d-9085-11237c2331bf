package com.actechx.gf.domain.service;

import com.actechx.common.utils.JsonUtils;
import com.actechx.gf.adapter.client.rest.CtNotificationClient;
import com.actechx.gf.adapter.controller.form.*;
import com.actechx.gf.domain.enums.NotificationChannel;
import com.actechx.gf.domain.enums.NotificationType;
import com.actechx.gf.domain.enums.TargetClient;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class NotificationMessageService {
  private final CtNotificationClient ctNotificationClient;

  public void processPricingRequest(String rawData) {
    List<PricingRequestDto> pricingRequestDtos = JsonUtils.toList(rawData, PricingRequestDto.class);
    for (PricingRequestDto pricingRequestDto : pricingRequestDtos) {
      NotificationRequestDto notificationRequestDto = new NotificationRequestDto();
      notificationRequestDto.setNotificationType(NotificationType.PRICING_REQUEST);
      notificationRequestDto.setChannel(NotificationChannel.BOTH);

      NotificationRequestDto.Recipient recipient = new NotificationRequestDto.Recipient();
      recipient.setTenantId(Long.valueOf(pricingRequestDto.getTargetTenantId()));
      recipient.setClientType(TargetClient.VENDOR);
      notificationRequestDto.setRecipient(recipient);

      Map<String, Object> placeholders = new HashMap<>();
      placeholders.put("quotationAskCode", pricingRequestDto.getQuotationAskCode());
      notificationRequestDto.setPlaceholders(placeholders);
      ctNotificationClient.createNotification(notificationRequestDto);
    }
  }

  public void processConfirmPurchaseRequest(String rawData) {
    List<SaleOrderDto> saleOrderDtos = JsonUtils.toList(rawData, SaleOrderDto.class);
    for (SaleOrderDto saleOrderDto : saleOrderDtos) {
      NotificationRequestDto notificationRequestDto = new NotificationRequestDto();
      notificationRequestDto.setNotificationType(NotificationType.OTHER);
      notificationRequestDto.setChannel(NotificationChannel.BOTH);

      NotificationRequestDto.Recipient recipient = new NotificationRequestDto.Recipient();
      recipient.setTenantId(saleOrderDto.getSupplierId());
      recipient.setClientType(TargetClient.VENDOR);
      notificationRequestDto.setRecipient(recipient);

      Map<String, Object> placeholders = new HashMap<>();
      placeholders.put("purchaseOrderCode", saleOrderDto.getCode());
      notificationRequestDto.setPlaceholders(placeholders);
      ctNotificationClient.createNotification(notificationRequestDto);
    }
  }

  public void processDeliveredShipmentOrder(String rawData) {
    PurchaseOrderStageRequest purchaseOrderStageRequest =
        JsonUtils.toObject(rawData, PurchaseOrderStageRequest.class);
    for (var purchaseOrderCode : purchaseOrderStageRequest.getCodes()) {
      NotificationRequestDto notificationRequestDto = new NotificationRequestDto();
      notificationRequestDto.setNotificationType(NotificationType.OTHER);
      notificationRequestDto.setChannel(NotificationChannel.BOTH);

      NotificationRequestDto.Recipient recipient = new NotificationRequestDto.Recipient();
      recipient.setTenantId(1L); // TODO
      recipient.setClientType(TargetClient.VENDOR);
      notificationRequestDto.setRecipient(recipient);

      Map<String, Object> placeholders = new HashMap<>();
      placeholders.put("purchaseOrderCode", purchaseOrderCode);
      notificationRequestDto.setPlaceholders(placeholders);
      ctNotificationClient.createNotification(notificationRequestDto);
    }
  }

  public void processCancelPurchaseOrders(String rawData) {
    CancelPurchaseRequestDto pricingRequestDtos =
        JsonUtils.toObject(rawData, CancelPurchaseRequestDto.class);
    NotificationRequestDto notificationRequestDto = new NotificationRequestDto();
    notificationRequestDto.setNotificationType(NotificationType.OTHER);
    notificationRequestDto.setChannel(NotificationChannel.BOTH);

    NotificationRequestDto.Recipient recipient = new NotificationRequestDto.Recipient();
    recipient.setTenantId(1L); // TODO
    recipient.setClientType(TargetClient.VENDOR);
    notificationRequestDto.setRecipient(recipient);

    Map<String, Object> placeholders = new HashMap<>();
    placeholders.put("purchaseOrderCode", "1"); // TODO
    notificationRequestDto.setPlaceholders(placeholders);
    ctNotificationClient.createNotification(notificationRequestDto);
  }
}
