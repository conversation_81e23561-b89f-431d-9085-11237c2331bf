package com.actechx.gf.domain.repository;

import com.actechx.gf.domain.model.OutboundMessage;
import java.util.List;

public interface OutboundMessageRepository {

  OutboundMessage save(OutboundMessage message);

  List<OutboundMessage> findPendingMessages(int limit);

  List<OutboundMessage> findRetryableMessages(int maxAttempts, int limit);

  List<OutboundMessage> findPendingBatch(int batchSize);

  void lockForProcessing(List<Long> ids);
}
