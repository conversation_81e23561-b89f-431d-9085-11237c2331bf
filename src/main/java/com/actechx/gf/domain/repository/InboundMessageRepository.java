package com.actechx.gf.domain.repository;

import com.actechx.gf.domain.model.InboundMessage;
import java.util.List;
import java.util.Optional;

public interface InboundMessageRepository {

  InboundMessage save(InboundMessage message);

  Optional<InboundMessage> findByMessageKey(String messageKey);

  List<InboundMessage> findPendingMessages(int limit);

  List<InboundMessage> findRetryableMessages(int maxAttempts, int limit);

  List<InboundMessage> findUnnotifiedMessages(int limit);

  List<InboundMessage> findPendingBatch(int batchSize);

  void lockForProcessing(List<Long> ids);
}
