package com.actechx.gf.domain.model;

import com.actechx.gf.domain.enums.OutboundMessageType;
import com.actechx.gf.domain.enums.ProcessingStatus;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class OutboundMessage {
  private Long id;
  private OutboundMessageType messageType;
  private Long tenantId;
  private String messageCode;
  private String payload;
  private String messageGroup;
  private String messageStep;
  private Long originTenantId;
  private ProcessingStatus status;
  private Integer attemptCount;
  private String lastError;
  private LocalDateTime createdAt;
  private LocalDateTime updatedAt;
  private LocalDateTime processedAt;

  public OutboundMessage(
      OutboundMessageType messageType,
      Long tenantId,
      String messageCode,
      String payload,
      String messageGroup,
      String messageStep,
      String originTenantId) {
    this.messageType = messageType;
    this.tenantId = tenantId;
    this.messageCode = messageCode;
    this.payload = payload;
    this.messageGroup = messageGroup;
    this.messageStep = messageStep;
    this.originTenantId = Long.parseLong(originTenantId);
    this.status = ProcessingStatus.PENDING;
    this.attemptCount = 0;
    this.createdAt = LocalDateTime.now();
    this.updatedAt = LocalDateTime.now();
  }

  public void markCompleted() {
    this.status = ProcessingStatus.COMPLETED;
    this.processedAt = LocalDateTime.now();
    this.updatedAt = LocalDateTime.now();
  }

  public void markFailed(String error) {
    this.status = ProcessingStatus.FAILED;
    this.lastError = error;
    this.updatedAt = LocalDateTime.now();
  }

  public void incrementAttempt() {
    this.attemptCount++;
    this.updatedAt = LocalDateTime.now();
  }
}
