package com.actechx.gf.domain.model;

import com.actechx.gf.domain.enums.InboundMessageType;
import com.actechx.gf.domain.enums.ProcessingStatus;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class InboundMessage {
  private Long id;
  private String messageKey;
  private InboundMessageType messageType;
  private Long tenantId;
  private String messageCode;
  private String payload;
  private String messageGroup;
  private String messageStep;
  private Long originTenantId;
  private ProcessingStatus status;
  private Integer attemptCount;
  private String lastError;
  private Boolean isNotified;
  private LocalDateTime createdAt;
  private LocalDateTime updatedAt;
  private LocalDateTime processedAt;

  public InboundMessage(
      String messageKey,
      InboundMessageType messageType,
      Long tenantId,
      String messageCode,
      String payload,
      String messageGroup,
      String messageStep,
      Long originTenantId) {
    this.messageKey = messageKey;
    this.messageType = messageType;
    this.tenantId = tenantId;
    this.messageCode = messageCode;
    this.payload = payload;
    this.messageGroup = messageGroup;
    this.messageStep = messageStep;
    this.status = ProcessingStatus.PENDING;
    this.originTenantId = originTenantId;
    this.attemptCount = 0;
    this.isNotified = false;
    this.createdAt = LocalDateTime.now();
    this.updatedAt = LocalDateTime.now();
  }

  public void markCompleted() {
    this.status = ProcessingStatus.COMPLETED;
    this.processedAt = LocalDateTime.now();
    this.updatedAt = LocalDateTime.now();
  }

  public void markFailed(String error) {
    this.status = ProcessingStatus.FAILED;
    this.lastError = error;
    this.updatedAt = LocalDateTime.now();
  }

  public void markNotified() {
    this.isNotified = true;
    this.updatedAt = LocalDateTime.now();
  }

  public void incrementAttempt() {
    this.attemptCount++;
    this.updatedAt = LocalDateTime.now();
  }
}
