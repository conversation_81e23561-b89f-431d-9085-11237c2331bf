package com.actechx.gf.app.service;

import com.actechx.gf.domain.enums.MessageGroup;
import com.actechx.gf.domain.enums.OutboundMessageType;
import com.actechx.gf.domain.enums.QuotationMessageStep;
import com.actechx.gf.domain.service.OutboundMessageService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Service
@Transactional
public class SimpleMessagingApplicationService {

  private final OutboundMessageService outboundService;

  public void createQuotationAskMessage(String askCode, Long tenantId, String jsonPayload) {
    outboundService.createMessage(
        OutboundMessageType.QUOTATION_ASK,
        tenantId,
        askCode,
        jsonPayload,
        MessageGroup.QUOTATION.name(),
        QuotationMessageStep.ASK_1.getStep());
  }

  public void createPricingRequestMessage(String askCode, Long tenantId, String payload) {
    outboundService.createMessage(
        OutboundMessageType.PRICING_REQUEST,
        tenantId,
        askCode,
        payload,
        MessageGroup.QUOTATION.name(),
        QuotationMessageStep.PRICE_1.getStep());
  }

  public void createPurchaseRequestMessage(String askCode, Long tenantId, String payload) {
    outboundService.createMessage(
        OutboundMessageType.CREATE_PURCHASE_REQUEST,
        tenantId,
        askCode,
        payload,
        MessageGroup.PO.name(),
        QuotationMessageStep.WAIT_TO_CONFIRM_1.getStep());
  }

  public void confirmPurchaseRequestMessage(String askCode, Long tenantId, String payload) {
    outboundService.createMessage(
        OutboundMessageType.CONFIRM_PURCHASE_REQUEST,
        tenantId,
        askCode,
        payload,
        MessageGroup.PO.name(),
        QuotationMessageStep.WAIT_TO_CONFIRM_1.getStep());
  }

  public void cancelPurchaseRequestMessage(String askCode, Long tenantId, String payload) {
    outboundService.createMessage(
        OutboundMessageType.CANCEL_PURCHASE_REQUEST,
        tenantId,
        askCode,
        payload,
        MessageGroup.PO.name(),
        QuotationMessageStep.CANCEL_1.getStep());
  }

  public void confirmDeliveredShipmentOrderMessage(String askCode, Long tenantId, String payload) {
    outboundService.createMessage(
        OutboundMessageType.DELIVERED_SHIPMENT_ORDER,
        tenantId,
        askCode,
        payload,
        MessageGroup.PO.name(),
        QuotationMessageStep.CLOSE_1.getStep());
  }
}
