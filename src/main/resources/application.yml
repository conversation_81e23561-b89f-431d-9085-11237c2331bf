server:
  port: ${SERVER_PORT:8080}
  servlet:
      context-path: ${CONTEXT_PATH:}

kafka:
  bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:b-2.nonprodaccluster.5qthmy.c2.kafka.ap-southeast-1.amazonaws.com:9092,b-1.nonprodaccluster.5qthmy.c2.kafka.ap-southeast-1.amazonaws.com:9092}
  consumer:
    group-id: ${KAFKA_CONSUMER_GROUP_ID:gf-erp-agent-group}
    shipment-group-id: ${KAFKA_CONSUMER_SHIPMENT_GROUP_ID:ac-nonprod-dev-to-info-gf-cg}
    shipment-stage-group-id: ${KAFKA_CONSUMER_SHIPMENT_UPDATE_GROUP_ID:ac-nonprod-dev-to-stage-gf-cg}
    auto-offset-reset: ${KAFKA_CONSUMER_AUTO_OFFSET_RESET:earliest}
    enable-auto-commit: ${KAFKA_CONSUMER_ENABLE_AUTO_COMMIT:false}
    auto-commit-interval: ${KAFKA_CONSUMER_AUTO_COMMIT_INTERVAL:1000}
  topics:
    quotation-ask: ${QUOTATION_ASK_TOPIC:AC-DEV-QUOTATION-ASK}
    quotation-bid: ${QUOTATION_BID_TOPIC:AC-DEV-QUOTATION-BID}
    quotation-ask-update: ${QUOTATION_ASK_UPDATE_TOPIC:AC-DEV-QUOTATION-ASK-UPDATE}
    pricing: ${PRICING_TOPIC:AC-DEV-PRICING}
    purchase-request: ${PURCHASE_REQUEST_TOPIC:AC-DEV-PURCHASE-REQUEST}
    order-stage-update: ${ORDER_STAGE_UPDATE_TOPIC:AC-DEV-ORDER-STAGE-UPDATE}
    sale-order: ${SALE_ORDER_TOPIC:AC-DEV-SALE-ORDER}
    location: ${LOCATION_TOPIC:AC-DEV-TENANT-ACTIVATION}
    shipment-order: ${SHIPMENT_ORDER_TOPIC:AC-NONPROD-DEV-TO-INFO}
    shipment-order-stage: ${SHIPMENT_ORDER_STAGE_TOPIC:AC-NONPROD-DEV-TO-STAGE}
    delivered-order: ${DELIVERVED_ORDER_TOPIC:AC-NONPROD-DEV-O-STAGE}

scheduler:
  simple:
    messaging:
      enabled: ${SCHEDULER_SIMPLE_MESSAGING_ENABLED:true}
      batch-size: ${SCHEDULER_SIMPLE_MESSAGING_BATCH_SIZE:10}
      max-retry-attempts: ${SCHEDULER_SIMPLE_MESSAGING_MAX_RETRY_ATTEMPTS:5}
      outbound:
        delay: ${SCHEDULER_SIMPLE_MESSAGING_OUTBOUND_DELAY:10000} # Process outbound every 10s
      inbound:
        delay: ${SCHEDULER_SIMPLE_MESSAGING_INBOUND_DELAY:10000}  # Process inbound every 10s
      notification:
        delay: ${SCHEDULER_SIMPLE_MESSAGING_NOTIFICATION_DELAY:15000} # Process notification every 15s

spring:
  application:
    name: gf-erp-agent
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:postgres}
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:postgres}
    driverClassName: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      leak-detection-threshold: 60000
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        temp.use_jdbc_metadata_defaults: false
        default_schema: ${DB_SCHEMA:dev-gf-erp-agent}
    open-in-view: false
  flyway:
    enabled: true
    validate-on-migrate: true
    schemas: ${DB_SCHEMA:dev-gf-erp-agent}
    default-schema: ${DB_SCHEMA:dev-gf-erp-agent}

  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  cloud:
    aws:
      region:
        static: ${AWS_REGION:ap-southeast-1}
      credentials:
        access-key: ${AWS_ACCESS_KEY:}
        secret-key: ${AWS_SECRET_KEY:}
      sns:
        endpoint: ${AWS_SNS_ENDPOINT:}
      sqs:
        endpoint: ${AWS_SQS_ENDPOINT:}

actechx:
  security:
    public-api:
      enabled: false

internal-service.api-key: ${INTERNAL_API_KEY:}

gf-purchase-service:
  url: ${GF_PURCHASE_SERVICE_URL:http://dev-gfds-gf-purchase.dev-saas-app.svc.cluster.local:8080/gf-purchase}

gf-shipment-service:
  url: ${GF_SHIPMENT_SERVICE_URL:http://dev-gfds-gf-shipment.dev-saas-app.svc.cluster.local:8080/gf-shipment}

gf-inventory-service:
  url: ${GF_INVENTORY_SERVICE_URL:http://dev-gfds-gf-inventory.dev-saas-app.svc.cluster.local:8080/gf-inventory}

ct-notification-service:
  url: ${CT_NOTIFICATION_SERVICE_URL:http://dev-nh-ct-notihub-notification.dev-non-saas-app.svc.cluster.local:8080/ct-notihub-notification}

# Management endpoints
management:
  #  server.port: ${HEALTH_PORT:8079}
  tracing:
    enabled: true
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  #      base-path: /
  endpoint:
    health:
      show-details: when-authorized
  prometheus:
    metrics:
      export:
        enabled: true

