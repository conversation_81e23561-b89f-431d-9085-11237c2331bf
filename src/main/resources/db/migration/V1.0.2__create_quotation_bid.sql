
CREATE TABLE quotation_bid (
   id BIGSERIAL PRIMARY KEY,
   original_id BIGINT NOT NULL,
   tenant_id BIGINT NOT NULL,
   payload JSON NOT NULL,
   quotation_ask_code VARCHAR(255) NOT NULL,
   target_class VARCHAR(255) NOT NULL,
   processing_status VARCHAR(20) NOT NULL,
   attempt_count INTEGER NOT NULL DEFAULT 0,
   last_error_message TEXT,
   is_notified BOOLEAN NOT NULL DEFAULT FALSE,
   created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
   updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
   processed_at TIMESTAMP
);

CREATE INDEX idx_quotation_bid_status ON quotation_bid(processing_status);
CREATE INDEX idx_quotation_bid_tenant ON quotation_bid(tenant_id);
CREATE INDEX idx_quotation_bid_created ON quotation_bid(created_at);