CREATE TABLE sequences (
   sequence_name VARCHAR(100) PRIMARY KEY,
   current_value BIGINT NOT NULL DEFAULT 0,
   increment_by INTEGER NOT NULL DEFAULT 1,
   created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
   updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE OR REPLACE PROCEDURE get_next_id(
    IN sequenceName VARCHAR(100),
    OUT nextId BIGINT
)
LANGUAGE plpgsql
AS $$
BEGIN
    -- Approach 1: Thử update trước
    UPDATE sequences
    SET current_value = current_value + increment_by,
        updated_at = CURRENT_TIMESTAMP
    WHERE sequence_name = sequenceName
    RETURNING current_value INTO nextId;

    -- Nếu không có row nào được update (sequence chưa tồn tại)
    IF NOT FOUND THEN
        -- Insert sequence mới với handling conflict
        INSERT INTO sequences (sequence_name, current_value, increment_by, created_at, updated_at)
        VALUES (sequenceName, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        ON CONFLICT (sequence_name) DO UPDATE
          SET current_value = sequences.current_value + sequences.increment_by,
            updated_at = CURRENT_TIMESTAMP
          RETURNING current_value INTO nextId;
    END IF;

END;
$$;

-- Create index for performance
CREATE INDEX idx_sequences_sequence_name ON sequences(sequence_name);


CREATE TABLE quotation_ask (
   id BIGSERIAL PRIMARY KEY,
   original_id BIGINT NOT NULL,
   tenant_id BIGINT NOT NULL,
   payload JSON NOT NULL,
   code VARCHAR(255) NOT NULL,
   target_class VARCHAR(255) NOT NULL,
   processing_status VARCHAR(20) NOT NULL,
   attempt_count INTEGER NOT NULL DEFAULT 0,
   last_error_message TEXT,
   created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
   updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
   processed_at TIMESTAMP
);

CREATE INDEX idx_quotation_ask_status ON quotation_ask(processing_status);
CREATE INDEX idx_quotation_ask_tenant ON quotation_ask(tenant_id);
CREATE INDEX idx_quotation_ask_created ON quotation_ask(created_at);