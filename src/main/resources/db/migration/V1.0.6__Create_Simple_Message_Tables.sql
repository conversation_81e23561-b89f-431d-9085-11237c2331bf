-- Outbound messages (for SNS publishing)
CREATE TABLE IF NOT EXISTS outbound_message (
    id BIGSERIAL PRIMARY KEY,
    message_type VARCHAR(50) NOT NULL,
    tenant_id BIGINT,
    message_code VARCHAR(255),
    payload JSON,
    message_group VARCHAR(50),
    message_step VARCHAR(50),
    origin_tenant_id BIGINT,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    attempt_count INTEGER NOT NULL DEFAULT 0,
    last_error TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP
);

-- Inbound messages (for SQS processing and API calls)
CREATE TABLE IF NOT EXISTS inbound_message (
    id BIGSERIAL PRIMARY KEY,
    message_key VARCHAR(500) NOT NULL UNIQUE,
    message_type VARCHAR(50) NOT NULL,
    tenant_id BIGINT,
    message_code <PERSON><PERSON><PERSON><PERSON>(255),
    payload JSON,
    message_group VARCHAR(50),
    message_step VARCHAR(50),
    origin_tenant_id BIGINT,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    attempt_count INTEGER NOT NULL DEFAULT 0,
    last_error TEXT,
    is_notified BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP
);