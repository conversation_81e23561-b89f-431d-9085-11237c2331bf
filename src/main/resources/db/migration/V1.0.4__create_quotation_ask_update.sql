ALTER TABLE quotation_ask DROP COLUMN original_id;
ALTER TABLE quotation_bid DROP COLUMN original_id;

CREATE TABLE quotation_ask_update (
   id BIGSERIAL PRIMARY KEY,
   tenant_id BIGINT NOT NULL,
   payload JSON NOT NULL,
   code VA<PERSON>HAR(255) NOT NULL,
   target_class VARCHAR(255) NOT NULL,
   processing_status VARCHAR(20) NOT NULL,
   attempt_count INTEGER NOT NULL DEFAULT 0,
   last_error_message TEXT,
   is_notified BOOLEAN NOT NULL DEFAULT FALSE,
   created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
   updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
   processed_at TIMESTAMP
);

CREATE INDEX idx_quotation_ask_updated_status ON quotation_ask_update(processing_status);