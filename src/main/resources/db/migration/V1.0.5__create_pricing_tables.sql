CREATE TABLE pricing_request (
   id BIGSERIAL PRIMARY KEY,
   origin_tenant_id BIGINT NOT NULL,
   payload JSON NOT NULL,
   quotation_ask_code VARCHAR(255) NOT NULL,
   target_class VARCHAR(255) NOT NULL,
   processing_status VARCHAR(20) NOT NULL,
   attempt_count INTEGER NOT NULL DEFAULT 0,
   last_error_message TEXT,
   is_notified BOOLEAN NOT NULL DEFAULT FALSE,
   created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
   updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
   processed_at TIMESTAMP
);

CREATE INDEX idx_pricing_request_status ON pricing_request(processing_status);

CREATE TABLE pricing_proposal (
    id BIGSERIAL PRIMARY KEY,
    reply_tenant_id BIGINT NOT NULL,
    payload JSON NOT NULL,
    quotation_ask_code VARCHAR(255) NOT NULL,
    target_class VARCHAR(255) NOT NULL,
    processing_status VARCHAR(20) NOT NULL,
    attempt_count INTEGER NOT NULL DEFAULT 0,
    last_error_message TEXT,
    is_notified BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP
);

CREATE INDEX idx_pricing_proposal_status ON pricing_proposal(processing_status);