plugins {
	id 'java'
	id 'org.springframework.boot' version '3.5.0'
	id 'io.spring.dependency-management' version '1.1.7'
	id 'jacoco'
	id 'com.diffplug.spotless' version '7.0.3'
}

group = 'com.actechx'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
}

repositories {
	maven {
		url "https://registry.devops.cardoctor.com.vn/repository/maven-snapshots/"
		credentials {
			username = findProperty("nexusUsername") ?: System.getenv("NEXUS_USERNAME")
			password = findProperty("nexusPassword") ?: System.getenv("NEXUS_PASSWORD")
		}
	}
	mavenCentral()
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-validation'
	implementation 'com.nimbusds:nimbus-jose-jwt:9.37.3'
	implementation 'org.flywaydb:flyway-core'
	implementation 'org.flywaydb:flyway-database-postgresql'
	implementation 'org.postgresql:postgresql:42.7.2'
	implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.8.9'
	implementation 'org.springframework.retry:spring-retry'
	implementation 'org.springframework:spring-aspects'
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'com.vladmihalcea:hibernate-types-60:2.21.1'

	implementation 'com.fasterxml.jackson.core:jackson-databind:2.17.1'
	implementation 'io.jsonwebtoken:jjwt-api:0.11.5'
	implementation 'io.jsonwebtoken:jjwt-impl:0.11.5'
	implementation 'io.jsonwebtoken:jjwt-jackson:0.11.5'

	// Kafka
	implementation 'org.springframework.kafka:spring-kafka'
	implementation 'org.apache.kafka:kafka-clients'

	// AWS SDK
	implementation 'io.awspring.cloud:spring-cloud-aws-starter-sns:3.4.0'
	implementation 'io.awspring.cloud:spring-cloud-aws-starter-sqs:3.4.0'
	implementation 'org.springframework.cloud:spring-cloud-aws-messaging:2.2.6.RELEASE'

	// MapStruct
	implementation 'org.mapstruct:mapstruct:1.5.5.Final'
	annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final'

	// If you're using both Lombok and MapStruct, you need this additional processor
	annotationProcessor 'org.projectlombok:lombok-mapstruct-binding:0.2.0'

	implementation 'com.actechx.common:common-classes:0.0.38-SNAPSHOT'
	implementation 'com.actechx.common:spring-security-starter:0.0.3-SNAPSHOT'

	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

tasks.named('test') {
	useJUnitPlatform()
}

spotless {
	java {
		googleJavaFormat('1.27.0')
	}
}

tasks.build {
	dependsOn tasks.named("spotlessApply")
}
